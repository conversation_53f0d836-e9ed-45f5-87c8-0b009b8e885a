import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"

export default function NotFound() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        <section className="w-full py-12 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Brand Not Found</h1>
                <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
                  Sorry, we couldn't find the brand you're looking for.
                </p>
              </div>
              <div className="space-y-2">
                <Link href="/">
                  <Button className="bg-black text-white hover:bg-gray-800">
                    Back to Home
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
} 