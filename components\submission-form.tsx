"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { EnhancedTextarea } from "@/components/ui/enhanced-textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Link, Upload, Search, X, Flower } from "lucide-react"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"

interface Brand {
  id: string
  name: string
  logo?: string
  domain: string
  category: string
  flawaCount: number
}

// Mock data - in a real app, this would come from your database
const mockBrands: Brand[] = [
  {
    id: "apple",
    name: "<PERSON>",
    domain: "apple.com",
    category: "Technology",
    flawaCount: 1243,
  },
  {
    id: "nike",
    name: "<PERSON>",
    domain: "nike.com",
    category: "Fashion",
    flawaCount: 987,
  },
  {
    id: "spotify",
    name: "Spotify",
    domain: "spotify.com",
    category: "Entertainment",
    flawaCount: 756,
  },
  {
    id: "tesla",
    name: "Tesla",
    domain: "tesla.com",
    category: "Automotive",
    flawaCount: 642,
  },
  {
    id: "airbnb",
    name: "Airbnb",
    domain: "airbnb.com",
    category: "Travel",
    flawaCount: 531,
  },
  {
    id: "starbucks",
    name: "Starbucks",
    domain: "starbucks.com",
    category: "Food & Beverage",
    flawaCount: 428,
  },
  {
    id: "amazon",
    name: "Amazon",
    domain: "amazon.com",
    category: "E-commerce",
    flawaCount: 2156,
  },
  {
    id: "google",
    name: "Google",
    domain: "google.com",
    category: "Technology",
    flawaCount: 1876,
  },
  {
    id: "microsoft",
    name: "Microsoft",
    domain: "microsoft.com",
    category: "Technology",
    flawaCount: 1543,
  },
  {
    id: "netflix",
    name: "Netflix",
    domain: "netflix.com",
    category: "Entertainment",
    flawaCount: 1298,
  },
]

// Format flawa count (e.g., 1000 -> 1K)
const formatCount = (count: number): string => {
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
  }
  return count.toString()
}

// Get favicon URL as fallback for brand logo
const getFaviconUrl = (domain: string, size = 128): string => {
  return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
}

interface SubmissionFormProps {
  inDialog?: boolean
  selectedBrandId?: string
}

export function SubmissionForm({ inDialog = false, selectedBrandId }: SubmissionFormProps) {
  const [submissionType, setSubmissionType] = useState("text")
  const [searchTerm, setSearchTerm] = useState("")
  const [searchResults, setSearchResults] = useState<Brand[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null)
  const [socialLinks, setSocialLinks] = useState({
    twitter: "",
    instagram: "",
    linkedin: "",
  })
  const [tags, setTags] = useState<string[]>([])
  const [tagInput, setTagInput] = useState("")
  const MAX_TAGS = 5
  const MAX_TAG_LENGTH = 20
  const [messageContent, setMessageContent] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({})
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Set the selected brand if selectedBrandId is provided
  useEffect(() => {
    if (selectedBrandId) {
      const brand = mockBrands.find((b) => b.id === selectedBrandId)
      if (brand) {
        setSelectedBrand(brand)
        // Pre-populate the search field with the brand name
        setSearchTerm(brand.name)
      }
    }
  }, [selectedBrandId])

  useEffect(() => {
    if (searchTerm.length > 1) {
      setIsSearching(true)
      // Filter brands based on search term
      const results = mockBrands.filter(
        (brand) =>
          brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          brand.category.toLowerCase().includes(searchTerm.toLowerCase()),
      )
      setSearchResults(results)
    } else {
      setIsSearching(false)
      setSearchResults([])
    }
  }, [searchTerm])

  const handleBrandSelect = (brand: Brand) => {
    setSelectedBrand(brand)
    setSearchTerm("")
    setIsSearching(false)
  }

  const handleTagInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      addTag()
    }
  }

  const addTag = () => {
    const trimmedInput = tagInput.trim()

    // Don't add empty tags
    if (!trimmedInput) return

    // Don't add duplicate tags (case insensitive)
    if (tags.some(tag => tag.toLowerCase() === trimmedInput.toLowerCase())) {
      setTagInput('')
      return
    }

    // Limit number of tags
    if (tags.length >= MAX_TAGS) {
      toast?.error(`Maximum ${MAX_TAGS} tags allowed`)
      setTagInput('')
      return
    }

    // Limit tag length
    const finalTag = trimmedInput.length > MAX_TAG_LENGTH
      ? trimmedInput.substring(0, MAX_TAG_LENGTH)
      : trimmedInput

    setTags([...tags, finalTag])
    setTagInput('')
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (value.endsWith(',')) {
      setTagInput(value.slice(0, -1))
      addTag()
    } else {
      setTagInput(value)
    }
  }

  const validateForm = (): boolean => {
    const errors: {[key: string]: string} = {}

    // Validate brand selection
    if (!selectedBrand) {
      errors.brand = "Please select a brand"
    }

    // Validate message content
    if (!messageContent.trim()) {
      errors.message = "Please enter your message"
    } else if (messageContent.length > 500) {
      errors.message = "Message is too long (maximum 500 characters)"
    }

    // Set errors and return validation result
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!validateForm()) {
      // Show error toast if validation fails
      toast.error("Please fix the errors in the form")
      return
    }

    // Set submitting state
    setIsSubmitting(true)

    try {
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1000))

      // In a real implementation, you would send data to an API endpoint
      // const response = await fetch('/api/flawas', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     brandId: selectedBrand?.id,
      //     message: messageContent,
      //     submissionType,
      //     socialLinks,
      //     tags,
      //     anonymous: false // Add anonymous option if needed
      //   })
      // })

      // if (!response.ok) throw new Error('Failed to submit flawa')

      // Show success message
      toast.success("Thank you for your submission! Your flawa will be reviewed by our team.")

      // Reset form
      setSelectedBrand(null)
      setSubmissionType("text")
      setMessageContent("")
      setSocialLinks({
        twitter: "",
        instagram: "",
        linkedin: "",
      })
      setTags([])
      setTagInput("")
      setFormErrors({})

      // Close dialog if in dialog mode
      if (inDialog) {
        // Find the closest dialog element and use the ESC key to close it
        const closeEvent = new KeyboardEvent('keydown', {
          key: 'Escape',
          bubbles: true
        });
        document.dispatchEvent(closeEvent);
      }
    } catch (error) {
      console.error("Error submitting flawa:", error)
      toast.error("Failed to submit your flawa. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className={inDialog ? "" : "w-full py-6 md:py-12"}>
      <div className={inDialog ? "" : "container px-4 md:px-6"}>
        {!inDialog && (
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Send a Flawa</h2>
              <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
                Share your love for a brand. No account required.
              </p>
            </div>
          </div>
        )}
        <Card className={`${inDialog ? "" : "max-w-2xl mx-auto mt-8"} border-border shadow-card rounded-xl`}>
          {!inDialog && (
            <CardHeader>
              <CardTitle>Submit Your Praise</CardTitle>
              <CardDescription>Your submission will be reviewed by our team before being published.</CardDescription>
            </CardHeader>
          )}
          <CardContent className={inDialog ? "pt-4" : ""}>
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-2">
                <Label htmlFor="brand">Brand Name</Label>
                {!selectedBrand ? (
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="brand"
                      placeholder="Search for a brand..."
                      className={`pl-10 ${formErrors.brand ? 'border-red-500' : ''}`}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      aria-describedby={formErrors.brand ? "brand-error" : undefined}
                    />
                    {formErrors.brand && (
                      <p id="brand-error" className="text-xs text-red-500 mt-1">
                        {formErrors.brand}
                      </p>
                    )}
                  </div>
                ) : null}

                {isSearching && !selectedBrand && (
                  <Card className="absolute z-10 w-full mt-1 max-h-[200px] overflow-y-auto">
                    <CardContent className="p-2">
                      {searchResults.length > 0 ? (
                        <div className="space-y-2">
                          {searchResults.map((brand) => (
                            <div
                              key={brand.id}
                              className="flex items-center p-2 hover:bg-muted rounded-lg transition-colors cursor-pointer"
                              onClick={() => handleBrandSelect(brand)}
                            >
                              <div className="rounded-xl overflow-hidden bg-muted flex items-center justify-center w-10 h-10 mr-3">
                                <Image
                                  src={brand.logo || getFaviconUrl(brand.domain)}
                                  alt={brand.name}
                                  width={40}
                                  height={40}
                                  className="brand-logo"
                                />
                              </div>
                              <div className="flex-1">
                                <h3 className="font-medium">{brand.name}</h3>
                                <div className="flex items-center mt-1 space-x-2">
                                  <Badge variant="outline" className="text-xs">
                                    {brand.category}
                                  </Badge>
                                  <span className="text-xs text-text-secondary flex items-center">
                                    <Flower className="h-3 w-3 mr-1" />
                                    {formatCount(brand.flawaCount)}
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="p-4 text-center text-text-secondary">
                          <p>No brands found matching "{searchTerm}"</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {selectedBrand && (
                  <div className="mt-2 p-3 bg-muted rounded-lg flex items-center">
                    <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-12 h-12 mr-4">
                      <Image
                        src={selectedBrand.logo || getFaviconUrl(selectedBrand.domain)}
                        alt={selectedBrand.name}
                        width={48}
                        height={48}
                        className="brand-logo"
                      />
                    </div>
                    <div>
                      <h3 className="font-medium">{selectedBrand.name}</h3>
                      <div className="flex items-center mt-1 space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {selectedBrand.category}
                        </Badge>
                        <span className="text-xs text-text-secondary flex items-center">
                          <Flower className="h-3 w-3 mr-1" />
                          {formatCount(selectedBrand.flawaCount)}
                        </span>
                      </div>
                    </div>
                    {!selectedBrandId && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="ml-auto"
                        onClick={() => setSelectedBrand(null)}
                        type="button"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label>Submission Type</Label>
                <RadioGroup defaultValue="text" className="flex flex-wrap gap-4" onValueChange={setSubmissionType}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="text" id="text" />
                    <Label htmlFor="text">Text</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="link" id="link" />
                    <Label htmlFor="link">Link (Tweet, Article)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="image" id="image" />
                    <Label htmlFor="image">Image</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="video" id="video" />
                    <Label htmlFor="video">Video Link</Label>
                  </div>
                </RadioGroup>
              </div>

              {submissionType === "text" && (
                <div className="space-y-2">
                  <Label htmlFor="content">Your Praise</Label>
                  <EnhancedTextarea
                    id="content"
                    ref={textareaRef}
                    placeholder="Share why you love this brand..."
                    className={`min-h-32 ${formErrors.message ? 'border-red-500' : ''}`}
                    maxLength={500}
                    value={messageContent}
                    onChange={(e) => setMessageContent(e.target.value)}
                    onEnhancedChange={(value) => setMessageContent(value)}
                    aria-describedby={formErrors.message ? "content-error" : "content-counter"}
                  />
                  {formErrors.message ? (
                    <p id="content-error" className="text-xs text-red-500">
                      {formErrors.message}
                    </p>
                  ) : (
                    <p id="content-counter" className={`text-xs ${messageContent.length > 480 ? 'text-amber-600' : 'text-muted-foreground'} text-right`}>
                      {messageContent.length}/500 characters
                    </p>
                  )}
                </div>
              )}

              {submissionType === "link" && (
                <div className="space-y-2">
                  <Label htmlFor="link">Link URL</Label>
                  <div className="flex">
                    <div className="relative flex-1">
                      <Link className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input id="link" placeholder="https://..." className="pl-8" />
                    </div>
                  </div>
                </div>
              )}

              {submissionType === "image" && (
                <div className="space-y-2">
                  <Label htmlFor="file">Upload Image</Label>
                  <div className="border-2 border-dashed border-border rounded-lg p-6 flex flex-col items-center justify-center">
                    <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm text-text-secondary mb-2">
                      Drag and drop your image here, or click to browse
                    </p>
                    <Input id="file" type="file" accept="image/*" className="hidden" />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => document.getElementById("file")?.click()}
                      type="button"
                    >
                      Browse Files
                    </Button>
                  </div>
                </div>
              )}

              {submissionType === "video" && (
                <div className="space-y-2">
                  <Label htmlFor="videoLink">Video Link</Label>
                  <div className="relative">
                    <Link className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="videoLink"
                      placeholder="https://youtube.com/... or https://vimeo.com/..."
                      className="pl-8"
                    />
                  </div>
                  <p className="text-xs text-text-secondary">
                    Please provide a link to your video on YouTube, Vimeo, or other video platforms.
                  </p>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="name">Your Name</Label>
                <Input id="name" placeholder="How you want to be credited" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Tags (separate with commas, max {MAX_TAGS})</Label>
                <div className="relative">
                  <Input
                    id="tags"
                    placeholder="e.g., customer service, product quality, innovation"
                    value={tagInput}
                    onChange={handleTagInputChange}
                    onKeyDown={handleTagInput}
                    onBlur={addTag}
                    disabled={tags.length >= MAX_TAGS}
                    aria-describedby="tags-description"
                  />
                </div>
                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="flex items-center gap-1 bg-muted"
                      >
                        {tag}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => removeTag(tag)}
                          aria-label={`Remove tag ${tag}`}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
                <p id="tags-description" className="text-xs text-text-secondary">
                  Add relevant tags ({tags.length}/{MAX_TAGS}) to categorize your flawa. These help others find similar content.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Your Email (Optional)</Label>
                <Input id="email" type="email" placeholder="For notification when published" />
              </div>

              <div className="space-y-2">
                <Label>Your Social Media (Optional)</Label>
                <div className="space-y-3">
                  <div className="relative">
                    <div className="absolute left-2.5 top-2.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-muted-foreground"
                      >
                        <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                      </svg>
                    </div>
                    <Input
                      placeholder="Twitter username"
                      className="pl-10"
                      value={socialLinks.twitter}
                      onChange={(e) => setSocialLinks({ ...socialLinks, twitter: e.target.value })}
                    />
                  </div>
                  <div className="relative">
                    <div className="absolute left-2.5 top-2.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-muted-foreground"
                      >
                        <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                        <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                        <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                      </svg>
                    </div>
                    <Input
                      placeholder="Instagram username"
                      className="pl-10"
                      value={socialLinks.instagram}
                      onChange={(e) => setSocialLinks({ ...socialLinks, instagram: e.target.value })}
                    />
                  </div>
                  <div className="relative">
                    <div className="absolute left-2.5 top-2.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-muted-foreground"
                      >
                        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                        <rect width="4" height="12" x="2" y="9" />
                        <circle cx="4" cy="4" r="2" />
                      </svg>
                    </div>
                    <Input
                      placeholder="LinkedIn username"
                      className="pl-10"
                      value={socialLinks.linkedin}
                      onChange={(e) => setSocialLinks({ ...socialLinks, linkedin: e.target.value })}
                    />
                  </div>
                </div>
              </div>

              <div className="text-sm text-text-secondary">
                <p>
                  By submitting, you acknowledge that Flawagram curates content from various online sources including
                  Product Hunt, Facebook, Twitter, and review websites.
                </p>
              </div>
            </form>
          </CardContent>
          <CardFooter>
            <Button
              className="w-full flex items-center gap-2 bg-black text-white hover:bg-gray-800 border border-black"
              type="submit"
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Sending...</span>
                </>
              ) : (
                <>
                  <Flower className="h-4 w-4" />
                  <span>Send a Flawa</span>
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
