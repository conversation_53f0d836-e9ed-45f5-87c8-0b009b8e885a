"use client"

import { useState } from "react"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Bell,
  Building,
  ChevronDown,
  Download,
  Eye,
  Flower,
  MoreHorizontal,
  Search,
  Settings,
  Star,
  Trash,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import Image from "next/image"

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  // Mock data
  const stats = {
    totalBrands: 156,
    pendingBrands: 12,
    totalFlawas: 4328,
    pendingFlawas: 87,
    brandsChange: 8,
    flawasChange: 42,
  }

  const recentBrands = [
    {
      id: "1",
      name: "Apple",
      domain: "apple.com",
      category: "Technology",
      status: "claimed",
      tier: "Pro",
      flawaCount: 1243,
    },
    {
      id: "2",
      name: "Nike",
      domain: "nike.com",
      category: "Fashion",
      status: "claimed",
      tier: "Basic",
      flawaCount: 987,
    },
    {
      id: "3",
      name: "Spotify",
      domain: "spotify.com",
      category: "Entertainment",
      status: "unclaimed",
      tier: "Free",
      flawaCount: 756,
    },
    {
      id: "4",
      name: "Tesla",
      domain: "tesla.com",
      category: "Automotive",
      status: "pending",
      tier: "Free",
      flawaCount: 642,
    },
    {
      id: "5",
      name: "Airbnb",
      domain: "airbnb.com",
      category: "Travel",
      status: "claimed",
      tier: "Pro",
      flawaCount: 531,
    },
  ]

  const recentFlawas = [
    {
      id: "1",
      content:
        "I've been using Apple products for over a decade now and I'm still amazed by how seamless the ecosystem is.",
      author: "Sarah Johnson",
      brand: "Apple",
      date: "2023-05-15",
      status: "approved",
    },
    {
      id: "2",
      content: "Just got my new Nike running shoes and they're amazing! Perfect fit and so comfortable.",
      author: "Mike Runner",
      brand: "Nike",
      date: "2023-05-14",
      status: "approved",
    },
    {
      id: "3",
      content: "Spotify's discovery algorithm knows me better than my friends do. It's scary good!",
      author: "Alex Music",
      brand: "Spotify",
      date: "2023-05-13",
      status: "pending",
    },
    {
      id: "4",
      content: "Tesla's customer service is exceptional. They went above and beyond to help me.",
      author: "Chris Green",
      brand: "Tesla",
      date: "2023-05-12",
      status: "pending",
    },
    {
      id: "5",
      content: "Just had the most amazing experience with Airbnb support. They helped me find the perfect place!",
      author: "Jamie Smith",
      brand: "Airbnb",
      date: "2023-05-11",
      status: "approved",
    },
  ]

  // Get favicon URL as fallback for brand logo
  const getFaviconUrl = (domain: string, size = 128): string => {
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 bg-muted/30">
        <div className="container px-4 md:px-6 py-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <div>
              <h1 className="text-2xl font-bold">Admin Dashboard</h1>
              <p className="text-text-secondary">Manage brands, flawas, and platform settings.</p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Bell className="h-4 w-4" />
                <span>Notifications</span>
              </Button>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Settings className="h-4 w-4" />
                <span>Settings</span>
              </Button>
            </div>
          </div>

          <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 md:grid-cols-5 lg:grid-cols-6 w-full">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="brands">Brands</TabsTrigger>
              <TabsTrigger value="flawas">Flawas</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="users" className="hidden md:block">
                Users
              </TabsTrigger>
              <TabsTrigger value="settings" className="hidden lg:block">
                Settings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Brands</CardTitle>
                    <Building className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.totalBrands}</div>
                    <p className="text-xs text-muted-foreground">+{stats.brandsChange} new this month</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Pending Brands</CardTitle>
                    <Building className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.pendingBrands}</div>
                    <p className="text-xs text-muted-foreground">Awaiting approval</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Flawas</CardTitle>
                    <Flower className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.totalFlawas.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground">+{stats.flawasChange} new this month</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Pending Flawas</CardTitle>
                    <Flower className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.pendingFlawas}</div>
                    <p className="text-xs text-muted-foreground">Awaiting moderation</p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Brands</CardTitle>
                    <CardDescription>Recently added or updated brands.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Brand</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Tier</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {recentBrands.map((brand) => (
                          <TableRow key={brand.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 rounded-full overflow-hidden bg-muted">
                                  <Image
                                    src={getFaviconUrl(brand.domain) || "/placeholder.svg"}
                                    alt={brand.name}
                                    width={24}
                                    height={24}
                                    className="object-cover"
                                  />
                                </div>
                                <span>{brand.name}</span>
                              </div>
                            </TableCell>
                            <TableCell>{brand.category}</TableCell>
                            <TableCell>
                              <Badge variant={brand.status === "pending" ? "outline" : "default"}>
                                {brand.status.charAt(0).toUpperCase() + brand.status.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell>{brand.tier}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    <span>View</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Star className="mr-2 h-4 w-4" />
                                    <span>Feature</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <Trash className="mr-2 h-4 w-4" />
                                    <span>Delete</span>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Recent Flawas</CardTitle>
                    <CardDescription>Recently submitted flawas.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Content</TableHead>
                          <TableHead>Author</TableHead>
                          <TableHead>Brand</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {recentFlawas.map((flawa) => (
                          <TableRow key={flawa.id}>
                            <TableCell className="max-w-[200px] truncate">{flawa.content}</TableCell>
                            <TableCell>{flawa.author}</TableCell>
                            <TableCell>{flawa.brand}</TableCell>
                            <TableCell>
                              <Badge variant={flawa.status === "pending" ? "outline" : "default"}>
                                {flawa.status.charAt(0).toUpperCase() + flawa.status.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    <span>View</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Star className="mr-2 h-4 w-4" />
                                    <span>Approve</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <Trash className="mr-2 h-4 w-4" />
                                    <span>Reject</span>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="brands" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>All Brands</CardTitle>
                  <CardDescription>Manage all brands on the platform.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <div className="relative">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input placeholder="Search brands..." className="pl-8 w-[250px]" />
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="flex items-center gap-1">
                            <span>Filter</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>All</DropdownMenuItem>
                          <DropdownMenuItem>Claimed</DropdownMenuItem>
                          <DropdownMenuItem>Unclaimed</DropdownMenuItem>
                          <DropdownMenuItem>Pending</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <Button className="flex items-center gap-1">
                      <Download className="h-4 w-4" />
                      <span>Export</span>
                    </Button>
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Brand</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Tier</TableHead>
                        <TableHead>Flawas</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recentBrands.map((brand) => (
                        <TableRow key={brand.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-2">
                              <div className="w-6 h-6 rounded-full overflow-hidden bg-muted">
                                <Image
                                  src={getFaviconUrl(brand.domain) || "/placeholder.svg"}
                                  alt={brand.name}
                                  width={24}
                                  height={24}
                                  className="object-cover"
                                />
                              </div>
                              <span>{brand.name}</span>
                            </div>
                          </TableCell>
                          <TableCell>{brand.category}</TableCell>
                          <TableCell>
                            <Badge variant={brand.status === "pending" ? "outline" : "default"}>
                              {brand.status.charAt(0).toUpperCase() + brand.status.slice(1)}
                            </Badge>
                          </TableCell>
                          <TableCell>{brand.tier}</TableCell>
                          <TableCell>{brand.flawaCount}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Star className="mr-2 h-4 w-4" />
                                  <span>Feature</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Trash className="mr-2 h-4 w-4" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="flawas" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>All Flawas</CardTitle>
                  <CardDescription>Manage all flawas on the platform.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <div className="relative">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input placeholder="Search flawas..." className="pl-8 w-[250px]" />
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="flex items-center gap-1">
                            <span>Filter</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>All</DropdownMenuItem>
                          <DropdownMenuItem>Approved</DropdownMenuItem>
                          <DropdownMenuItem>Pending</DropdownMenuItem>
                          <DropdownMenuItem>Rejected</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <Button className="flex items-center gap-1">
                      <Download className="h-4 w-4" />
                      <span>Export</span>
                    </Button>
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Content</TableHead>
                        <TableHead>Author</TableHead>
                        <TableHead>Brand</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recentFlawas.map((flawa) => (
                        <TableRow key={flawa.id}>
                          <TableCell className="max-w-[300px] truncate">{flawa.content}</TableCell>
                          <TableCell>{flawa.author}</TableCell>
                          <TableCell>{flawa.brand}</TableCell>
                          <TableCell>{new Date(flawa.date).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <Badge variant={flawa.status === "pending" ? "outline" : "default"}>
                              {flawa.status.charAt(0).toUpperCase() + flawa.status.slice(1)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Star className="mr-2 h-4 w-4" />
                                  <span>Approve</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Trash className="mr-2 h-4 w-4" />
                                  <span>Reject</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      <Footer />
    </div>
  )
}
