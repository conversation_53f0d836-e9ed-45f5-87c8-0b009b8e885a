"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { FlawaCard } from "@/components/ui/flawa-card"
import { Flawa, SharePlatform } from "@/types"
import { getAllFlawas } from "@/data/flawas"

// Format flawa count (e.g., 1000 -> 1K)
const formatCount = (count: number): string => {
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
  }
  return count.toString()
}

// Get favicon URL as fallback for brand logo
const getFaviconUrl = (domain: string, size = 128): string => {
  return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
}

export function FlawaShowcase() {
  // Get all flawas and sort by timestamp to show most recent
  const recentFlawas = getAllFlawas()
    .sort((a, b) => new Date(b.timestamp || '').getTime() - new Date(a.timestamp || '').getTime())
    .slice(0, 6) // Show 6 most recent flawas

  const handleShare = (flawa: Flawa, platform: SharePlatform) => {
    const shareUrl = `https://www.flawagram.com/${flawa.brand.toLowerCase()}`
    const shareText = `Check out this amazing flawa about ${flawa.brand} on Flawa! ${flawa.content.substring(0, 100)}...`

    switch (platform) {
      case "twitter":
        window.open(
          `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`,
          "_blank"
        )
        break
      case "facebook":
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`, "_blank")
        break
      case "linkedin":
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`, "_blank")
        break
      case "copy":
        navigator.clipboard.writeText(shareUrl)
        alert("Link copied to clipboard!")
        break
    }
  }

  return (
    <section className="w-full py-12 md:py-24 bg-muted">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Recent Flawas</h2>
            <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
              See the latest love and praise shared by the community.
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
          {recentFlawas.map((flawa) => (
            <FlawaCard key={flawa.id} flawa={flawa} onShare={handleShare} />
          ))}
        </div>
        <div className="flex justify-center mt-10">
          <Link href="/all-flawas">
            <Button className="bg-black text-white hover:bg-gray-800">
              View More Flawas
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
