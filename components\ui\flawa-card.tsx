import Image from "next/image"
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Share2, Twitter, Instagram, Linkedin, Youtube, Link as <PERSON><PERSON><PERSON>, Flower } from "lucide-react" // Added Youtube, LinkIcon, Flower
import { FlawaCardProps } from "@/types"
import { toast } from "sonner"

const getFaviconUrl = (domain: string, size: number = 32): string => {
  return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
}

// Helper function to get the source icon and label
const getSourceIcon = (sourceUrl: string | undefined, flawaId: string): { Icon: React.ElementType; label: string; url: string } => {
  const flawagramBaseUrl = "https://www.flawagram.com"; // Define base URL for clarity
  const flawaPath = `/flawas/${flawaId}`; // Assuming this is the path to a specific Flawa

  // Default to Flawagram icon and specific Flawa URL if sourceUrl is not provided or empty
  if (!sourceUrl || sourceUrl.trim() === "") {
    return { Icon: Flower, label: "View on Flawagram", url: `${flawagramBaseUrl}${flawaPath}` };
  }

  try {
    const url = new URL(sourceUrl);
    const hostname = url.hostname.toLowerCase();

    // Check for Flawagram first - if sourceUrl is a Flawagram URL, use it directly.
    // This handles cases where the source IS a Flawagram URL but might be different from the canonical /flawas/:id one.
    if (hostname.includes("flawagram.com")) {
      return { Icon: Flower, label: "View on Flawagram", url: sourceUrl };
    }
    // For other external sites
    if (hostname.includes("youtube.com") || hostname.includes("youtu.be")) {
      return { Icon: Youtube, label: "View on YouTube", url: sourceUrl };
    }
    if (hostname.includes("twitter.com") || hostname.includes("x.com")) {
      return { Icon: Twitter, label: "View on X/Twitter", url: sourceUrl };
    }
    if (hostname.includes("instagram.com")) {
      return { Icon: Instagram, label: "View on Instagram", url: sourceUrl };
    }
    if (hostname.includes("linkedin.com")) {
      return { Icon: Linkedin, label: "View on LinkedIn", url: sourceUrl };
    }
    // Default to generic link icon for other external sources
    return { Icon: LinkIcon, label: "View Source", url: sourceUrl };
  } catch (e) {
    console.error("Invalid source URL:", sourceUrl, e);
    // Fallback for invalid URLs - still provide the original URL for the user to attempt
    return { Icon: LinkIcon, label: "View Source (link may be invalid)", url: sourceUrl };
  }
};

const defaultShareHandler = async (flawa: FlawaCardProps["flawa"]): Promise<void> => {
  const shareUrl = `https://www.flawagram.com/${flawa.brand.toLowerCase()}`;
  const shareText = `Check out this amazing flawa about ${flawa.brand}: "${flawa.content.substring(0, 50)}..."`;
  const shareTitle = `Flawa from ${flawa.brand}`;

  if (navigator.share) {
    try {
      await navigator.share({
        title: shareTitle,
        text: shareText,
        url: shareUrl,
      });
      toast.success("Shared successfully!");
    } catch (shareError) {
      if ((shareError as Error).name !== 'AbortError') {
        // If sharing fails (and not due to user abort), try copying the link silently
        try {
          await navigator.clipboard.writeText(shareUrl);
          toast.success("Link copied to clipboard! (Sharing not available)");
        } catch (copyError) {
          // If both share and copy fail, then show an error
          toast.error("Couldn't share or copy link", {
            description: "Please copy this URL manually: " + shareUrl,
            action: {
              label: "Copy Manually",
              onClick: () => {
                const tempInput = document.createElement("input");
                tempInput.value = shareUrl;
                document.body.appendChild(tempInput);
                tempInput.select();
                try {
                  document.execCommand("copy");
                  toast.success("Link copied to clipboard!");
                } catch (manualCopyError) {
                  toast.error("Manual copy failed. Please copy: " + shareUrl);
                }
                document.body.removeChild(tempInput);
              },
            },
          });
        }
      }
      // If it was an AbortError, do nothing (user cancelled)
    }
  } else {
    // Fallback for browsers that don't support Web Share API: Copy link
    try {
      await navigator.clipboard.writeText(shareUrl);
      toast.success("Link copied to clipboard! (Web Share not supported)");
    } catch (err) {
      // Further fallback for clipboard API issues (e.g. insecure context)
      toast.error("Couldn't copy link automatically", {
        description: "Please copy this URL manually: " + shareUrl,
        action: {
          label: "Copy Manually",
          onClick: () => {
            const tempInput = document.createElement("input");
            tempInput.value = shareUrl;
            document.body.appendChild(tempInput);
            tempInput.select();
            try {
              document.execCommand("copy");
              toast.success("Link copied to clipboard!");
            } catch (copyError) {
              toast.error("Manual copy failed. Please copy: " + shareUrl);
            }
            document.body.removeChild(tempInput);
          },
        },
      });
    }
  }
};

export function FlawaCard({
  flawa,
  variant = "default",
  onShare = defaultShareHandler,
  showBrandInfo = true
}: FlawaCardProps) {
  const sourceInfo = getSourceIcon(flawa.source, flawa.id); // Pass flawa.id

  return (
    <Card className={`overflow-hidden border-border shadow-card rounded-xl ${variant === "default" ? "hover-scale" : ""} flex flex-col h-full`}>
      <CardContent className="p-0 flex-grow">
        <div className="p-6">
          {showBrandInfo && (
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 rounded-full overflow-hidden">
                  <Image
                    src={getFaviconUrl(flawa.brandDomain, 32)}
                    alt={flawa.brand}
                    width={20}
                    height={20}
                    className="brand-logo"
                  />
                </div>
                <Badge variant="outline" className="text-xs">
                  {flawa.brand}
                </Badge>
              </div>
            </div>
          )}

          {flawa.image && (
            <div className="relative w-full h-48 mb-4">
              <Image
                src={flawa.image}
                alt={`Flawa by ${flawa.author}`}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className={`object-cover ${variant === "brand-page" ? "grayscale-image" : "flawa-image"}`}
              />
            </div>
          )}

          <div className={`${!flawa.image ? "mt-2" : ""}`}>
            <p className="text-base mb-4">{flawa.content}</p>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {flawa.authorImage && (
                <div className="w-8 h-8 rounded-full overflow-hidden mr-3">
                  <Image
                    src={flawa.authorImage}
                    alt={flawa.author}
                    width={32}
                    height={32}
                    className="object-cover"
                  />
                </div>
              )}
              <div className="text-sm font-medium">{flawa.author}</div>
            </div>
            
            {flawa.socialLinks && Object.keys(flawa.socialLinks).length > 0 && (
              <div className="flex items-center space-x-3">
                {flawa.socialLinks.twitter && (
                  <a
                    href={`https://twitter.com/${flawa.socialLinks.twitter.replace("@", "")}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Author's Twitter profile"
                    className="text-text-secondary hover:text-hover transition-colors"
                  >
                    <Twitter className="h-4 w-4" />
                  </a>
                )}
                {flawa.socialLinks.instagram && (
                  <a
                    href={`https://instagram.com/${flawa.socialLinks.instagram}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Author's Instagram profile"
                    className="text-text-secondary hover:text-hover transition-colors"
                  >
                    <Instagram className="h-4 w-4" />
                  </a>
                )}
                {flawa.socialLinks.linkedin && (
                  <a
                    href={`https://linkedin.com/in/${flawa.socialLinks.linkedin}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Author's LinkedIn profile"
                    className="text-text-secondary hover:text-hover transition-colors"
                  >
                    <Linkedin className="h-4 w-4" />
                  </a>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
      {variant !== "minimal" && (
        <CardFooter className="p-4 border-t flex justify-between items-center">
          {/* sourceInfo will always be defined now based on getSourceIcon's updated return type */}
          <a
            href={sourceInfo.url}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={sourceInfo.label}
            title={sourceInfo.label}
            className="flex items-center text-text-secondary hover:text-hover transition-colors"
          >
            <sourceInfo.Icon className="h-5 w-5 mr-1" />
            <span className="text-xs sr-only">{sourceInfo.label}</span> {/* Screen reader only text */}
          </a>
          <Button variant="ghost" size="sm" className="flex items-center" onClick={() => onShare(flawa)}>
            <Share2 className="h-4 w-4 mr-1" />
            <span className="text-sm">Share</span>
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}