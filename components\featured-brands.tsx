import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Flower } from "lucide-react"
import { getFeaturedBrands } from "@/data/brands"
import { Brand } from "@/types"

// Format flawa count (e.g., 1000 -> 1K)
const formatCount = (count: number): string => {
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
  }
  return count.toString()
}

// Get favicon URL as fallback for brand logo
const getFaviconUrl = (domain: string, size = 128): string => {
  return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
}

export function FeaturedBrands() {
  const featuredBrands = getFeaturedBrands()

  return (
    <section className="w-full py-12 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Featured Brands</h2>
            <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
              Discover brands with the most love and engagement on Flawagram.
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-8">
          {featuredBrands.map((brand) => (
            <Link key={brand.id} href={`/${brand.id}`} className="hover-scale">
              <Card className="overflow-hidden border-border shadow-card rounded-xl h-full">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center">
                    <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-16 h-16 mb-4">
                      <Image
                        src={brand.logo || getFaviconUrl(brand.domain)}
                        alt={brand.name}
                        width={64}
                        height={64}
                        className="brand-logo"
                      />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg">{brand.name}</h3>
                      <div className="flex items-center justify-center mt-2 space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {brand.category}
                        </Badge>
                      </div>
                      <div className="mt-2 flex items-center justify-center text-text-secondary">
                        <Flower className="h-4 w-4 mr-1" />
                        <span>{formatCount(brand.flawaCount)}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
        <div className="flex justify-center mt-10">
          <Link href="/brands">
            <Button className="bg-black text-white hover:bg-gray-800">
              View All Brands
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
