"use client"

import Link from "next/link"
import Image from "next/image"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Flower, Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { brands as allBrandsData } from "@/data/brands"
import { Brand } from "@/types"
import { GlobalFlawaButton } from "@/components/global-flawa-button"

// Format flawa count (e.g., 1000 -> 1K)
const formatCount = (count: number): string => {
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
  }
  return count.toString()
}

// Get favicon URL as fallback for brand logo
const getFaviconUrl = (domain: string, size = 128): string => {
  return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
}

const getAllBrands = (): Brand[] => {
  return Object.values(allBrandsData)
}

export default function AllBrandsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState<"popular" | "az">("popular")

  const allBrands = getAllBrands()

  const filteredAndSortedBrands = allBrands
    .filter((brand) => {
      const query = searchQuery.toLowerCase()
      return (
        brand.name.toLowerCase().includes(query) ||
        brand.category.toLowerCase().includes(query)
      )
    })
    .sort((a, b) => {
      if (sortBy === "popular") {
        return b.flawaCount - a.flawaCount
      } else if (sortBy === "az") {
        return a.name.localeCompare(b.name)
      }
      return 0
    })

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        <section className="w-full py-12 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">All Brands</h1>
                <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
                  Explore all the amazing brands on Flawagram.
                </p>
              </div>
            </div>

            <div className="space-y-8 mb-12">
              <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div className="flex-1 w-full sm:max-w-sm">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-text-secondary" />
                    <Input
                      type="search"
                      placeholder="Search brands..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={sortBy === "popular" ? "default" : "outline"}
                    onClick={() => setSortBy("popular")}
                  >
                    Most Popular
                  </Button>
                  <Button
                    variant={sortBy === "az" ? "default" : "outline"}
                    onClick={() => setSortBy("az")}
                  >
                    A-Z
                  </Button>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {filteredAndSortedBrands.map((brand) => (
                <Link key={brand.id} href={`/brands/${brand.id}`} className="hover-scale">
                  <Card className="overflow-hidden border-border shadow-card rounded-xl h-full">
                    <CardContent className="p-6">
                      <div className="flex flex-col items-center text-center">
                        <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-16 h-16 mb-4">
                          <Image
                            src={brand.logo || getFaviconUrl(brand.domain)}
                            alt={brand.name}
                            width={64}
                            height={64}
                            className="brand-logo"
                          />
                        </div>
                        <div>
                          <h3 className="font-bold text-lg">{brand.name}</h3>
                          <div className="flex items-center justify-center mt-2 space-x-2">
                            <Badge variant="outline" className="text-xs">
                              {brand.category}
                            </Badge>
                          </div>
                          <div className="mt-2 flex items-center justify-center text-text-secondary">
                            <Flower className="h-4 w-4 mr-1" />
                            <span>{formatCount(brand.flawaCount)}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </section>
      </main>
      <Footer />
      <GlobalFlawaButton />
    </div>
  )
}