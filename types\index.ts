/**
 * Social media links for a Flawa
 */
export interface SocialLinks {
  twitter?: string;
  instagram?: string;
  linkedin?: string;
}

/**
 * Represents a piece of brand appreciation content
 */
export interface Flawa {
  id: string;
  type: "tweet" | "image" | "video" | "text";
  content: string;
  author: string;
  authorImage?: string;
  brand: string;
  brandDomain: string;
  likes: number;
  source?: string;
  image?: string;
  timestamp?: string;
  socialLinks?: SocialLinks;
  isLiked?: boolean;
}

/**
 * Props for the FlawaCard component
 */
export interface FlawaCardProps {
  /** The Flawa content to display */
  flawa: Flawa;
  /** Visual style variant of the card */
  variant?: "default" | "minimal" | "brand-page";
  /** Callback when share button is clicked */
  onShare?: (flawa: Flawa) => Promise<void>;
  /** Whether to show brand information */
  showBrandInfo?: boolean;
}

// SharePlatform type is no longer needed as Web Share API handles platform choice

export interface Brand {
  id: string;
  name: string;
  logo?: string | null;
  domain: string;
  description?: string;
  website?: string;
  category: string;
  flawaCount: number;
  isClaimed?: boolean;
  tier?: "Free" | "Pro";
} 