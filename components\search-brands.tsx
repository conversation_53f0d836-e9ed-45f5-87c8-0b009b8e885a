"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Search, X } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface Brand {
  id: string
  name: string
  logo?: string
  domain: string
  category: string
  flawaCount: number
}

// Mock data - in a real app, this would come from your database
const mockBrands: Brand[] = [
  {
    id: "apple",
    name: "Apple",
    domain: "apple.com",
    category: "Technology",
    flawaCount: 1243,
  },
  {
    id: "nike",
    name: "<PERSON>",
    domain: "nike.com",
    category: "Fashion",
    flawaCount: 987,
  },
  {
    id: "spotify",
    name: "Spotify",
    domain: "spotify.com",
    category: "Entertainment",
    flawaCount: 756,
  },
  {
    id: "tesla",
    name: "Tesla",
    domain: "tesla.com",
    category: "Automotive",
    flawaCount: 642,
  },
  {
    id: "airbnb",
    name: "Airbnb",
    domain: "airbnb.com",
    category: "Travel",
    flawaCount: 531,
  },
  {
    id: "starbucks",
    name: "Starbucks",
    domain: "starbucks.com",
    category: "Food & Beverage",
    flawaCount: 428,
  },
  {
    id: "amazon",
    name: "Amazon",
    domain: "amazon.com",
    category: "E-commerce",
    flawaCount: 2156,
  },
  {
    id: "google",
    name: "Google",
    domain: "google.com",
    category: "Technology",
    flawaCount: 1876,
  },
  {
    id: "microsoft",
    name: "Microsoft",
    domain: "microsoft.com",
    category: "Technology",
    flawaCount: 1543,
  },
  {
    id: "netflix",
    name: "Netflix",
    domain: "netflix.com",
    category: "Entertainment",
    flawaCount: 1298,
  },
]

export function SearchBrands() {
  const [searchTerm, setSearchTerm] = useState("")
  const [searchResults, setSearchResults] = useState<Brand[]>([])
  const [isSearching, setIsSearching] = useState(false)

  useEffect(() => {
    if (searchTerm.length > 1) {
      setIsSearching(true)
      // Filter brands based on search term
      const results = mockBrands.filter(
        (brand) =>
          brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          brand.category.toLowerCase().includes(searchTerm.toLowerCase()),
      )
      setSearchResults(results)
    } else {
      setIsSearching(false)
      setSearchResults([])
    }
  }, [searchTerm])

  // Format flawa count (e.g., 1000 -> 1K)
  const formatCount = (count: number): string => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
    }
    return count.toString()
  }

  // Get favicon URL as fallback for brand logo
  const getFaviconUrl = (domain: string, size = 128): string => {
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
  }

  return (
    <div className="w-full max-w-3xl mx-auto relative">
      <div className="relative">
        <Search className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search for brands..."
          className="pl-10 py-6 text-lg"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        {searchTerm && (
          <Button variant="ghost" size="icon" className="absolute right-2 top-2" onClick={() => setSearchTerm("")}>
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>

      {isSearching && (
        <Card className="absolute w-full mt-2 z-50 max-h-[70vh] overflow-y-auto">
          <CardContent className="p-2">
            {searchResults.length > 0 ? (
              <div className="space-y-2">
                {searchResults.map((brand) => (
                  <Link key={brand.id} href={`/brands/${brand.id}`} className="block">
                    <div className="flex items-center p-3 hover:bg-muted rounded-lg transition-colors">
                      <div className="rounded-xl overflow-hidden bg-muted flex items-center justify-center w-12 h-12 mr-4">
                        <Image
                          src={brand.logo || getFaviconUrl(brand.domain)}
                          alt={brand.name}
                          width={48}
                          height={48}
                          className="grayscale-image"
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{brand.name}</h3>
                        <div className="flex items-center mt-1 space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {brand.category}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-text-secondary">
                <p>No brands found matching "{searchTerm}"</p>
                <Button className="mt-4" onClick={() => setSearchTerm("")}>
                  Send a Flawa for a new brand
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
