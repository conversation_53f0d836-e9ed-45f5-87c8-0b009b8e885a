"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  BarChart,
  ChevronDown,
  CreditCard,
  Download,
  Eye,
  Flower,
  MoreHorizontal,
  Settings,
  Star,
  Trash,
  TrendingUp,
  Upload,
} from "lucide-react"
import Image from "next/image"

export default function BrandDashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  // Mock data
  const brand = {
    name: "Apple",
    logo: null,
    domain: "apple.com",
    description:
      "Apple Inc. is an American multinational technology company that designs, develops, and sells consumer electronics, computer software, and online services.",
    website: "https://apple.com",
    category: "Technology",
    flawaCount: 1243,
    tier: "Pro",
    stats: {
      views: 12543,
      engagement: 3.2,
      flawasThisMonth: 42,
      viewsChange: 12,
      engagementChange: 0.3,
      flawasChange: 8,
    },
  }

  const recentFlawas = [
    {
      id: "1",
      content:
        "I've been using Apple products for over a decade now and I'm still amazed by how seamless the ecosystem is.",
      author: "Sarah Johnson",
      date: "2023-05-15",
      status: "approved",
      likes: 42,
    },
    {
      id: "2",
      content: "Just got my new MacBook Pro and I'm blown away by the performance. Apple silicon is a game changer!",
      author: "Tech Enthusiast",
      date: "2023-05-12",
      status: "approved",
      likes: 78,
    },
    {
      id: "3",
      content: "Apple's customer service is unmatched. Had an issue with my iPhone and they resolved it immediately.",
      author: "Loyal Customer",
      date: "2023-05-10",
      status: "pending",
      likes: 0,
    },
    {
      id: "4",
      content: "The attention to detail in Apple products is incredible. Every aspect is thoughtfully designed.",
      author: "Design Lover",
      date: "2023-05-08",
      status: "approved",
      likes: 92,
    },
    {
      id: "5",
      content: "Apple's commitment to privacy is why I trust them with my data. No other tech company comes close.",
      author: "Privacy Advocate",
      date: "2023-05-05",
      status: "approved",
      likes: 54,
    },
  ]

  // Get favicon URL as fallback for brand logo
  const getFaviconUrl = (domain: string, size = 128): string => {
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 bg-muted/30">
        <div className="container px-4 md:px-6 py-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <div className="flex items-center gap-4">
              <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-16 h-16 shadow-card">
                <Image
                  src={brand.logo || getFaviconUrl(brand.domain)}
                  alt={brand.name}
                  width={64}
                  height={64}
                  className="brand-logo"
                />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl font-bold">{brand.name}</h1>
                  <Badge>{brand.tier}</Badge>
                </div>
                <p className="text-text-secondary">{brand.domain}</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Settings className="h-4 w-4" />
                <span>Settings</span>
              </Button>
              <Button size="sm" className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                <span>View Public Page</span>
              </Button>
            </div>
          </div>

          <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 md:grid-cols-5 lg:grid-cols-6 w-full">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="flawas">Flawas</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="billing" className="hidden md:block">
                Billing
              </TabsTrigger>
              <TabsTrigger value="settings" className="hidden lg:block">
                Settings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{brand.stats.views.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground">
                      {brand.stats.viewsChange > 0 ? "+" : ""}
                      {brand.stats.viewsChange}% from last month
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{brand.stats.engagement}%</div>
                    <p className="text-xs text-muted-foreground">
                      {brand.stats.engagementChange > 0 ? "+" : ""}
                      {brand.stats.engagementChange}% from last month
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">New Flawas</CardTitle>
                    <Flower className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{brand.stats.flawasThisMonth}</div>
                    <p className="text-xs text-muted-foreground">
                      {brand.stats.flawasChange > 0 ? "+" : ""}
                      {brand.stats.flawasChange} from last month
                    </p>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Flawas</CardTitle>
                  <CardDescription>Your most recent flawas and their status.</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Content</TableHead>
                        <TableHead>Author</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recentFlawas.map((flawa) => (
                        <TableRow key={flawa.id}>
                          <TableCell className="max-w-[300px] truncate">{flawa.content}</TableCell>
                          <TableCell>{flawa.author}</TableCell>
                          <TableCell>{new Date(flawa.date).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <Badge variant={flawa.status === "approved" ? "default" : "outline"}>
                              {flawa.status === "approved" ? "Approved" : "Pending"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Star className="mr-2 h-4 w-4" />
                                  <span>Feature</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Trash className="mr-2 h-4 w-4" />
                                  <span>Hide</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline">Previous</Button>
                  <Button variant="outline">Next</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="flawas" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>All Flawas</CardTitle>
                  <CardDescription>Manage all flawas for your brand.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Input placeholder="Search flawas..." className="w-[250px]" />
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="flex items-center gap-1">
                            <span>Filter</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>All</DropdownMenuItem>
                          <DropdownMenuItem>Approved</DropdownMenuItem>
                          <DropdownMenuItem>Pending</DropdownMenuItem>
                          <DropdownMenuItem>Hidden</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <Button className="flex items-center gap-1">
                      <Download className="h-4 w-4" />
                      <span>Export</span>
                    </Button>
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Content</TableHead>
                        <TableHead>Author</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Likes</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recentFlawas.map((flawa) => (
                        <TableRow key={flawa.id}>
                          <TableCell className="max-w-[300px] truncate">{flawa.content}</TableCell>
                          <TableCell>{flawa.author}</TableCell>
                          <TableCell>{new Date(flawa.date).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <Badge variant={flawa.status === "approved" ? "default" : "outline"}>
                              {flawa.status === "approved" ? "Approved" : "Pending"}
                            </Badge>
                          </TableCell>
                          <TableCell>{flawa.likes}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Star className="mr-2 h-4 w-4" />
                                  <span>Feature</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Trash className="mr-2 h-4 w-4" />
                                  <span>Hide</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <div className="text-sm text-muted-foreground">Showing 5 of 42 flawas</div>
                  <div className="flex gap-2">
                    <Button variant="outline">Previous</Button>
                    <Button variant="outline">Next</Button>
                  </div>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Analytics</CardTitle>
                  <CardDescription>View detailed analytics for your brand.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px] flex items-center justify-center border rounded-md">
                    <div className="flex flex-col items-center gap-2 text-center">
                      <BarChart className="h-16 w-16 text-muted-foreground" />
                      <h3 className="text-lg font-medium">Analytics Dashboard</h3>
                      <p className="text-sm text-muted-foreground max-w-md">
                        Detailed analytics showing views, engagement, and flawa trends over time.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="profile" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Brand Profile</CardTitle>
                  <CardDescription>Update your brand information and public profile.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Brand Name</Label>
                    <Input id="name" defaultValue={brand.name} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" defaultValue={brand.description} className="min-h-32" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input id="website" defaultValue={brand.website} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Input id="category" defaultValue={brand.category} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="logo">Logo</Label>
                    <div className="flex items-center gap-4">
                      <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-16 h-16 shadow-card">
                        <Image
                          src={brand.logo || getFaviconUrl(brand.domain)}
                          alt={brand.name}
                          width={64}
                          height={64}
                          className="brand-logo"
                        />
                      </div>
                      <Button variant="outline" className="flex items-center gap-1">
                        <Upload className="h-4 w-4" />
                        <span>Upload New Logo</span>
                      </Button>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button>Save Changes</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="billing" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Billing & Subscription</CardTitle>
                  <CardDescription>Manage your subscription and billing information.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="rounded-lg border p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-medium">Current Plan</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge>Pro</Badge>
                          <span className="text-sm text-muted-foreground">$49/month</span>
                        </div>
                      </div>
                      <Button variant="outline">Change Plan</Button>
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <h3 className="font-medium mb-2">Payment Method</h3>
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      <span>•••• •••• •••• 4242</span>
                      <Badge variant="outline" className="ml-auto">
                        Expires 12/25
                      </Badge>
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <h3 className="font-medium mb-2">Billing History</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm font-medium">May 1, 2023</p>
                          <p className="text-xs text-muted-foreground">Pro Plan - Monthly</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">$49.00</p>
                          <Badge variant="outline" className="text-xs">
                            Paid
                          </Badge>
                        </div>
                      </div>
                      <Separator />
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm font-medium">Apr 1, 2023</p>
                          <p className="text-xs text-muted-foreground">Pro Plan - Monthly</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">$49.00</p>
                          <Badge variant="outline" className="text-xs">
                            Paid
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Settings</CardTitle>
                  <CardDescription>Manage your account settings and preferences.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Email Notifications</h3>
                        <p className="text-sm text-muted-foreground">Receive email notifications for new flawas.</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <Separator />
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Auto-Approve Flawas</h3>
                        <p className="text-sm text-muted-foreground">Automatically approve new flawas.</p>
                      </div>
                      <Switch />
                    </div>
                    <Separator />
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Public Profile</h3>
                        <p className="text-sm text-muted-foreground">Make your brand profile visible to the public.</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button>Save Settings</Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      <Footer />
    </div>
  )
}
