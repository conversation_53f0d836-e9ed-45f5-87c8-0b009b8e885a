import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON> } from "@/components/hero"
import { FeaturedBrands } from "@/components/featured-brands"
import { FlawaShowcase } from "@/components/flawa-showcase"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { GlobalFlawaButton } from "@/components/global-flawa-button"
import { Flower } from "lucide-react"

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        <Hero />

        <section className="w-full py-12 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-8">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter">What is a Flawa?</h2>
                <div className="flex items-center justify-center gap-2 mb-4">
                  <Flower className="h-6 w-6" />
                  <span className="text-xl font-medium">Flawa = Flow of Appreciation</span>
                </div>
                <p className="mx-auto max-w-[700px] text-text-secondary">
                  A "Flawa" is any expression of love or praise for a brand. We collect and curate Flawas from across
                  the internet, including social media, review sites, and direct submissions.
                </p>
              </div>
            </div>
          </div>
        </section>

        <FeaturedBrands />
        <section className="w-full py-12 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="space-y-4 text-center lg:text-left">
                <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">For Brands</div>
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Claim Your Flawagram</h2>
                <p className="text-text-secondary md:text-xl">
                  Subscribe to customize your brand's Flawagram page, highlight your favorite praise, and gain valuable
                  insights.
                </p>
                <div className="flex flex-col sm:flex-row justify-center lg:justify-start gap-3">
                  <Link href="/claim" className="w-full sm:w-auto">
                    <Button size="lg" className="w-full sm:w-auto flex items-center gap-2">
                      <Flower className="h-4 w-4" />
                      <span>Get Started</span>
                    </Button>
                  </Link>
                  <Link href="/about" className="w-full sm:w-auto">
                    <Button size="lg" className="w-full sm:w-auto flex items-center gap-2">
                      <Flower className="h-4 w-4" />
                      <span>Learn More</span>
                    </Button>
                  </Link>
                </div>
              </div>
              <div className="space-y-4">
                <ul className="grid gap-3 md:items-start items-center justify-center text-center md:text-left">
                  <li className="flex flex-col md:flex-row items-center md:items-start gap-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-black text-white shrink-0">1</div>
                    <div>
                      <h3 className="font-medium">Claim Your Brand Page</h3>
                      <p className="text-sm text-text-secondary">Take ownership of your public Flawagram profile.</p>
                    </div>
                  </li>
                  <li className="flex flex-col md:flex-row items-center md:items-start gap-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-black text-white shrink-0">2</div>
                    <div>
                      <h3 className="font-medium">Customize & Highlight</h3>
                      <p className="text-sm text-text-secondary">
                        Feature your favorite praise and customize your page.
                      </p>
                    </div>
                  </li>
                  <li className="flex flex-col md:flex-row items-center md:items-start gap-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-black text-white shrink-0">3</div>
                    <div>
                      <h3 className="font-medium">Gain Insights</h3>
                      <p className="text-sm text-text-secondary">Access analytics on views, engagement, and more.</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>
        <FlawaShowcase />
      </main>
      <Footer />
      <GlobalFlawaButton />
    </div>
  )
}
