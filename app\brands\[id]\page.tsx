"use client"

import { useState, useEffect, use } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import Image from "next/image"
import Link from "next/link"
import { Flower, Share2, Twitter, Instagram, Youtube, Globe, Search, X } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { FlawaCard } from "@/components/ui/flawa-card"
import { <PERSON>lawa } from "@/types" // Removed SharePlatform
import { toast } from "sonner"
import { SubmissionForm } from "@/components/submission-form"

// Format flawa count (e.g., 1000 -> 1K)
const formatCount = (count: number): string => {
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
  }
  return count.toString()
}

// Get favicon URL as fallback for brand logo
const getFaviconUrl = (domain: string, size = 128): string => {
  return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
}

export default function BrandPage({ params: paramsPromise }: { params: Promise<{ id: string }> }) {
  const params = use(paramsPromise);
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilter, setActiveFilter] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<"recent" | "popular">("recent")
  const [flawasToShow, setFlawasToShow] = useState(6);

  // Mock brands data - in a real app, this would come from a database
  const mockBrands = {
    apple: {
      id: "apple",
      name: "Apple",
      logo: null, // Using null to demonstrate favicon fallback
      domain: "apple.com",
      description:
        "Apple Inc. is an American multinational technology company that designs, develops, and sells consumer electronics, computer software, and online services.",
      website: "https://apple.com",
      category: "Technology",
      flawaCount: 1243,
      isClaimed: true,
      tier: "Pro",
    },
    nike: {
      id: "nike",
      name: "Nike",
      logo: null,
      domain: "nike.com",
      description:
        "Nike, Inc. is an American multinational corporation that designs, develops, manufactures, and markets footwear, apparel, equipment, accessories, and services.",
      website: "https://nike.com",
      category: "Fashion",
      flawaCount: 987,
      isClaimed: true,
      tier: "Pro",
    },
    spotify: {
      id: "spotify",
      name: "Spotify",
      logo: null,
      domain: "spotify.com",
      description:
        "Spotify is a digital music, podcast, and video streaming service that gives you access to millions of songs and other content from artists all over the world.",
      website: "https://spotify.com",
      category: "Entertainment",
      flawaCount: 756,
      isClaimed: false,
      tier: "Free",
    },
    tesla: {
      id: "tesla",
      name: "Tesla",
      logo: null,
      domain: "tesla.com",
      description:
        "Tesla, Inc. is an American electric vehicle and clean energy company that designs and manufactures electric cars, battery energy storage, and solar products.",
      website: "https://tesla.com",
      category: "Automotive",
      flawaCount: 642,
      isClaimed: true,
      tier: "Pro",
    },
    airbnb: {
      id: "airbnb",
      name: "Airbnb",
      logo: null,
      domain: "airbnb.com",
      description:
        "Airbnb, Inc. is an American company that operates an online marketplace for lodging, primarily homestays for vacation rentals, and tourism activities.",
      website: "https://airbnb.com",
      category: "Travel",
      flawaCount: 531,
      isClaimed: false,
      tier: "Free",
    },
  };

  // Get the brand data based on the id parameter
  // If the brand doesn't exist in our mock data, default to a generic brand
  const brand = mockBrands[params.id as keyof typeof mockBrands] || {
    id: params.id,
    name: params.id.charAt(0).toUpperCase() + params.id.slice(1),
    logo: null,
    domain: `${params.id}.com`,
    description: `This is the ${params.id} brand page.`,
    website: `https://${params.id}.com`,
    category: "Other",
    flawaCount: Math.floor(Math.random() * 1000),
    isClaimed: false,
    tier: "Free",
  }

  const allFlawas: Flawa[] = [
    {
      id: "1",
      type: "tweet",
      content:
        "I've been using @apple products for over a decade now and I'm still amazed by how seamless the ecosystem is. Best tech investment ever!",
      author: "Sarah Johnson",
      brand: brand.name,
      brandDomain: brand.domain,
      likes: 42,
      source: "Twitter",
      image: "/placeholder.svg?height=400&width=600",
      socialLinks: {
        twitter: "@sarahjohnson",
      },
      timestamp: "2023-01-15T10:30:00Z",
    },
    {
      id: "2",
      type: "image",
      content: "Just got my new MacBook Pro and I'm blown away by the performance. Apple silicon is a game changer!",
      author: "Tech Enthusiast",
      brand: brand.name,
      brandDomain: brand.domain,
      likes: 78,
      image: "/placeholder.svg?height=400&width=600",
      socialLinks: {
        instagram: "techenthusiast",
      },
      timestamp: "2023-02-20T14:00:00Z",
    },
    {
      id: "3",
      type: "text",
      content:
        "Apple's customer service is unmatched. Had an issue with my iPhone and they resolved it immediately. This is why I keep coming back!",
      author: "Loyal Customer",
      brand: brand.name,
      brandDomain: brand.domain,
      likes: 36,
      socialLinks: {},
      timestamp: "2023-03-10T09:15:00Z",
    },
    {
      id: "4",
      type: "video",
      content: "The attention to detail in Apple products is incredible. Every aspect is thoughtfully designed.",
      author: "Design Lover",
      brand: brand.name,
      brandDomain: brand.domain,
      likes: 92,
      image: "/placeholder.svg?height=400&width=600",
      socialLinks: {},
      timestamp: "2023-04-05T18:45:00Z",
    },
    {
      id: "5",
      type: "tweet",
      content: "Apple's commitment to privacy is why I trust them with my data. No other tech company comes close.",
      author: "Privacy Advocate",
      brand: brand.name,
      brandDomain: brand.domain,
      likes: 54,
      source: "Twitter",
      socialLinks: {
        twitter: "@privacyadvocate",
      },
      timestamp: "2023-05-01T12:00:00Z",
    },
    {
      id: "6",
      type: "image",
      content: "My Apple Watch literally saved my life by detecting an irregular heartbeat. Forever grateful.",
      author: "Health Conscious",
      brand: brand.name,
      brandDomain: brand.domain,
      likes: 128,
      image: "/placeholder.svg?height=400&width=600",
      socialLinks: {
        instagram: "healthconscious",
      },
      timestamp: "2023-06-10T08:00:00Z",
    },
  ];

  // Filter and sort flawas
  const displayedFlawas = allFlawas
    .filter(flawa => {
      const matchesSearch = searchQuery === "" || 
        flawa.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        flawa.author.toLowerCase().includes(searchQuery.toLowerCase())
        
      const matchesFilter = !activeFilter || 
        (activeFilter === "twitter" && flawa.socialLinks?.twitter) ||
        (activeFilter === "instagram" && flawa.socialLinks?.instagram) ||
        (activeFilter === "youtube" && flawa.type === "video")
        
      return matchesSearch && matchesFilter
    })
    .sort((a, b) => {
      if (sortBy === "recent") {
        // For "recent", sort by timestamp if available, otherwise by likes as a fallback
        const dateA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
        const dateB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
        if (dateB !== dateA) return dateB - dateA;
        return b.likes - a.likes; // Fallback to likes if timestamps are same or not present
      } else { // "popular"
        return b.likes - a.likes
      }
    })
    .slice(0, flawasToShow);

  const handleLoadMore = () => {
    setFlawasToShow(prev => prev + 6);
  };

  const handleShare = async (flawa: Flawa) => {
    const shareUrl = `https://www.flawagram.com/${flawa.brandDomain}`; // Use brandDomain for consistency
    const shareText = `Check out this amazing flawa about ${flawa.brand} on Flawagram! ${flawa.content.substring(0, 100)}...`;
    const shareTitle = `Flawa for ${flawa.brand}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: shareTitle,
          text: shareText,
          url: shareUrl,
        });
        toast.success("Shared successfully!");
      } catch (shareError) {
        if ((shareError as Error).name !== 'AbortError') {
          // If sharing fails (and not due to user abort), try copying the link silently
          try {
            await navigator.clipboard.writeText(shareUrl);
            toast.success("Link copied to clipboard! (Sharing not available)");
          } catch (copyError) {
            // If both share and copy fail, then show an error
            toast.error("Couldn't share or copy link", {
              description: "Please copy this URL manually: " + shareUrl,
            });
          }
        }
        // If it was an AbortError, do nothing (user cancelled)
      }
    } else {
      // Fallback for browsers that don't support Web Share API: Copy link
      try {
        await navigator.clipboard.writeText(shareUrl);
        toast.success("Link copied to clipboard! (Web Share not supported)");
      } catch (err) {
        toast.error("Couldn't copy link", {
          description: "Please copy this URL manually: " + shareUrl,
        });
      }
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        <section className="w-full py-12 md:py-24 bg-muted">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-[2fr,1fr] lg:gap-12">
              <div>
                <div className="flex items-center gap-4">
                  <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-16 h-16 md:w-24 md:h-24 shadow-sm">
                    <Image
                      src={brand.logo || getFaviconUrl(brand.domain)}
                      alt={brand.name}
                      width={96}
                      height={96}
                      className="brand-logo"
                    />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold md:text-4xl">{brand.name}</h1>
                    <div className="flex items-center mt-2 space-x-2">
                      <Badge variant="outline">{brand.category}</Badge>
                      <span className="text-sm text-text-secondary flex items-center">
                        <Flower className="h-4 w-4 mr-1" />
                        {formatCount(brand.flawaCount)} Flawas
                      </span>
                    </div>
                    <div className="mt-4">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button className="w-full sm:w-auto">
                            <Flower className="h-4 w-4 mr-2" />
                            Send a Flawa
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Send a Flawa to {brand.name}</DialogTitle>
                            <DialogDescription>
                              Share your positive experience with {brand.name}
                            </DialogDescription>
                          </DialogHeader>
                          <SubmissionForm selectedBrandId={brand.id} />
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col md:flex-row gap-3 mt-6 justify-center md:justify-start space-y-3 md:space-y-0 md:space-x-3">
                  {!brand.isClaimed && (
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button className="w-full md:w-auto flex items-center gap-2 bg-black text-white hover:bg-gray-800 border border-black">
                          Claim This Brand
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Claim {brand.name}</DialogTitle>
                          <DialogDescription>
                            Verify your ownership of {brand.name} to claim your brand profile.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="py-4">
                          <ClaimForm selectedBrandId={brand.id} mockBrands={mockBrands} />
                        </div>
                      </DialogContent>
                    </Dialog>
                  )}
                </div>

                <p className="mt-2 text-text-secondary max-w-2xl">{brand.description}</p>
                <div className="flex flex-wrap gap-4 mt-4 justify-center md:justify-start">
                  <div className="flex items-center">
                    <Badge variant="outline" className="text-xs">
                      {brand.category}
                    </Badge>
                  </div>
                  <div
                    className="flex items-center text-text-secondary text-sm"
                    title={`${formatCount(brand.flawaCount)} Flawas received`}
                  >
                    <Flower className="h-4 w-4 mr-1" />
                    <span>{formatCount(brand.flawaCount)}</span>
                  </div>
                  <a
                    href={brand.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-text-secondary hover:text-hover transition-colors flex items-center"
                    title={brand.website}
                  >
                    <Globe className="h-4 w-4" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="w-full py-12">
          <div className="container px-4 md:px-6">
            <div className="space-y-8">
              <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div className="flex-1 w-full sm:max-w-sm">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-text-secondary" />
                    <Input
                      type="search"
                      placeholder="Search flawas..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={sortBy === "recent" ? "default" : "outline"}
                    onClick={() => setSortBy("recent")}
                  >
                    Most Recent
                  </Button>
                  <Button
                    variant={sortBy === "popular" ? "default" : "outline"}
                    onClick={() => setSortBy("popular")}
                  >
                    Most Popular
                  </Button>
                </div>
              </div>

              <Tabs defaultValue="all" className="w-full">
                <TabsList className="w-full justify-start">
                  <TabsTrigger value="all" onClick={() => setActiveFilter(null)}>
                    All
                  </TabsTrigger>
                  <TabsTrigger value="twitter" onClick={() => setActiveFilter("twitter")}>
                    Twitter
                  </TabsTrigger>
                  <TabsTrigger value="instagram" onClick={() => setActiveFilter("instagram")}>
                    Instagram
                  </TabsTrigger>
                  <TabsTrigger value="youtube" onClick={() => setActiveFilter("youtube")}>
                    YouTube
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="space-y-8">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {displayedFlawas.map((flawa) => (
                      <FlawaCard
                        key={flawa.id}
                        flawa={flawa}
                        onShare={handleShare}
                        // variant="brand-page" // Optional: if you want a specific style for brand page cards
                        showBrandInfo={false} // Brand info is already prominent on the page
                      />
                    ))}
                  </div>
                </TabsContent>
              </Tabs>

              {flawasToShow < allFlawas.length && displayedFlawas.length > 0 && displayedFlawas.length >= flawasToShow && (
                <div className="flex justify-center mt-10">
                  <Button
                    onClick={handleLoadMore}
                    variant="default"
                    className="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-3 text-base"
                  >
                    Load More Flawas
                  </Button>
                </div>
              )}
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}

// Simple ClaimForm component that redirects to the claim page with the brand ID
function ClaimForm({ selectedBrandId, mockBrands }: { selectedBrandId: string; mockBrands: Record<string, any> }) {
  const [selectedBrand, setSelectedBrand] = useState<any>(null)
  const [verificationStep, setVerificationStep] = useState<'email' | 'fallback'>('email')
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: '',
    personalEmail: '',
    socialProfile: '',
    verificationMethod: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [emailDomainError, setEmailDomainError] = useState<string | null>(null)

  // Set the selected brand on component mount
  useEffect(() => {
    if (selectedBrandId && mockBrands) {
      // Find the brand in mockBrands using the selectedBrandId
      const brand = mockBrands[selectedBrandId as keyof typeof mockBrands]
      if (brand) {
        setSelectedBrand(brand)
      }
    }
  }, [selectedBrandId, mockBrands])

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Check if email domain matches brand domain
    const emailDomain = formData.email.split('@')[1]
    if (emailDomain !== selectedBrand?.domain) {
      setEmailDomainError(`Email domain must match ${selectedBrand?.domain}`)
      setIsSubmitting(false)
      return
    }

    // Simulate API call to send verification email
    setTimeout(() => {
      setIsSubmitting(false)
      // Show success message and next steps
      toast.success("Verification email sent! Please check your inbox.")
    }, 1000)
  }

  const handleFallbackSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call for manual verification
    setTimeout(() => {
      setIsSubmitting(false)
      // Show success message
      toast.success("Your verification request has been submitted. Our team will review it shortly.")
    }, 1000)
  }

  if (!selectedBrand) {
    return <div>Loading...</div>
  }

  return (
    <div className="space-y-6">
      <div className="space-y-6">
        {/* Brand Header */}
        <div className="p-4 border rounded-lg">
          <div className="flex items-center">
            <div className="mr-4 h-16 w-16 flex-shrink-0 overflow-hidden rounded-lg bg-gray-100">
              <Image
                src={selectedBrand.logo || getFaviconUrl(selectedBrand.domain)}
                alt={selectedBrand.name}
                width={64}
                height={64}
                className="h-full w-full object-contain"
              />
            </div>
            <div>
              <div className="font-medium text-lg">{selectedBrand.name}</div>
              <div className="text-sm text-gray-500">{selectedBrand.domain}</div>
            </div>
          </div>
        </div>

        {/* Verification Steps */}
        <div className="space-y-4">
          <div className="flex justify-between">
            <Button
              variant={verificationStep === 'email' ? 'default' : 'outline'}
              onClick={() => setVerificationStep('email')}
              className="flex-1 mr-2"
            >
              Email Verification
            </Button>
            <Button
              variant={verificationStep === 'fallback' ? 'default' : 'outline'}
              onClick={() => setVerificationStep('fallback')}
              className="flex-1 ml-2"
            >
              Alternative Verification
            </Button>
          </div>

          {verificationStep === 'email' ? (
            <form onSubmit={handleEmailSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Work Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  required
                />
                {emailDomainError && (
                  <p className="text-sm text-red-500">{emailDomainError}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Role at Company</Label>
                <Input
                  id="role"
                  value={formData.role}
                  onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? 'Sending Verification...' : 'Send Verification Email'}
              </Button>
            </form>
          ) : (
            <form onSubmit={handleFallbackSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="personalEmail">Personal Email</Label>
                <Input
                  id="personalEmail"
                  type="email"
                  value={formData.personalEmail}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, personalEmail: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="socialProfile">LinkedIn/Twitter Profile</Label>
                <Input
                  id="socialProfile"
                  value={formData.socialProfile}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, socialProfile: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="verificationMethod">How can we verify your association?</Label>
                <Textarea
                  id="verificationMethod"
                  value={formData.verificationMethod}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, verificationMethod: e.target.value })}
                  placeholder="Please explain how we can verify your association with the brand (e.g., company website listing, press releases, etc.)"
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit for Review'}
              </Button>
            </form>
          )}
        </div>
      </div>
    </div>
  )
}
