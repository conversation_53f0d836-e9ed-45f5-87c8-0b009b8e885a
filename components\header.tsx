"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import { Flower, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { SubmissionForm } from "@/components/submission-form"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useIsMobile } from "@/hooks/use-mobile"

interface HeaderProps {
  brand?: {
    id: string
    name: string
    [key: string]: any
  }
}

export function Header({ brand }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false)
  const isMobile = useIsMobile()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    // Set initial scroll state
    handleScroll()

    // Add event listener
    window.addEventListener("scroll", handleScroll)

    // Clean up event listener on component unmount
    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  return (
    <header
      className={`w-full transition-all duration-300 ${isScrolled ? "bg-background/95 backdrop-blur-sm border-b" : ""}`}
    >
      <div className="container flex h-16 items-center justify-between px-4 md:px-6">
        <div className="flex items-center gap-2">
          <Link href="/" className="flex items-center space-x-2">
            <Flower className="h-5 w-5 md:h-6 md:w-6" />
            <span className="text-xl md:text-2xl font-bold">Flawagram</span>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center gap-3">
          {brand?.isClaimed && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-1">
                  <span>My Brand</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href="/dashboard">Dashboard</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard?tab=analytics">Analytics</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard?tab=settings">Settings</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          <Dialog>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2 bg-black text-white hover:bg-gray-800 border border-black">
                <Flower className="h-4 w-4" />
                <span>Send a Flawa</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Send a Flawa</DialogTitle>
                <DialogDescription>Share your love for a brand. No account required.</DialogDescription>
              </DialogHeader>
              <SubmissionForm inDialog={true} selectedBrandId={brand?.id} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Mobile Send a Flawa Button */}
        <div className="flex md:hidden items-center">
          <Dialog>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2 bg-black text-white hover:bg-gray-800 border border-black">
                <Flower className="h-4 w-4" />
                <span>Send a Flawa</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Send a Flawa</DialogTitle>
                <DialogDescription>Share your love for a brand. No account required.</DialogDescription>
              </DialogHeader>
              <SubmissionForm inDialog={true} selectedBrandId={brand?.id} />
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </header>
  )
}
