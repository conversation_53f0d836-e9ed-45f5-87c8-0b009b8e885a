import { <PERSON>lawa } from "@/types"

export const flawas: Record<string, Flawa[]> = {
  apple: [
    {
      id: "apple-1",
      type: "text",
      content: "The MacBook Air M2 is a game-changer! The battery life is incredible, and it handles everything I throw at it with ease. Apple's silicon is truly revolutionary.",
      author: "<PERSON>",
      authorImage: "https://i.pravatar.cc/150?u=sarah",
      brand: "Apple",
      brandDomain: "apple.com",
      likes: 342,
      timestamp: "2024-03-10T14:23:00Z",
      socialLinks: {
        twitter: "@sarahtech",
        linkedin: "sarahchen"
      }
    },
    {
      id: "apple-2",
      type: "image",
      content: "My workspace setup with the new iMac. The colors bring so much joy to my desk!",
      author: "<PERSON>",
      authorImage: "https://i.pravatar.cc/150?u=mike",
      brand: "Apple",
      brandDomain: "apple.com",
      likes: 567,
      image: "https://images.unsplash.com/photo-1517059224940-d4af9eec41b7?w=800&h=600",
      timestamp: "2024-03-09T10:15:00Z",
      socialLinks: {
        instagram: "mikedesigns"
      }
    }
  ],
  tesla: [
    {
      id: "tesla-1",
      type: "text",
      content: "Just got my Model Y and I'm blown away by the autopilot features. The future of driving is here!",
      author: "Alex Thompson",
      authorImage: "https://i.pravatar.cc/150?u=alex",
      brand: "Tesla",
      brandDomain: "tesla.com",
      likes: 423,
      timestamp: "2024-03-08T16:45:00Z",
      socialLinks: {
        twitter: "@alexdrive",
        linkedin: "alexthompson"
      }
    },
    {
      id: "tesla-2",
      type: "image",
      content: "Road trip with zero emissions! 1000 miles and counting in my Model 3.",
      author: "Emma Davis",
      authorImage: "https://i.pravatar.cc/150?u=emma",
      brand: "Tesla",
      brandDomain: "tesla.com",
      likes: 789,
      image: "https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=800&h=600",
      timestamp: "2024-03-07T12:30:00Z"
    }
  ],
  spotify: [
    {
      id: "spotify-1",
      type: "text",
      content: "Spotify's Discover Weekly is like having a friend who knows your music taste better than you do. It's scary good!",
      author: "James Wilson",
      authorImage: "https://i.pravatar.cc/150?u=james",
      brand: "Spotify",
      brandDomain: "spotify.com",
      likes: 234,
      timestamp: "2024-03-06T09:20:00Z",
      socialLinks: {
        twitter: "@jameswmusic"
      }
    },
    {
      id: "spotify-2",
      type: "text",
      content: "The collaborative playlist feature has made my remote work sessions with colleagues so much more fun!",
      author: "Lisa Park",
      authorImage: "https://i.pravatar.cc/150?u=lisa",
      brand: "Spotify",
      brandDomain: "spotify.com",
      likes: 456,
      timestamp: "2024-03-05T15:10:00Z"
    }
  ],
  nike: [
    {
      id: "nike-1",
      type: "image",
      content: "These new Nike Air Zoom running shoes are a game-changer for my marathon training!",
      author: "Chris Martinez",
      authorImage: "https://i.pravatar.cc/150?u=chris",
      brand: "Nike",
      brandDomain: "nike.com",
      likes: 678,
      image: "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=800&h=600",
      timestamp: "2024-03-04T11:25:00Z",
      socialLinks: {
        instagram: "chrismrun"
      }
    },
    {
      id: "nike-2",
      type: "text",
      content: "Nike's commitment to sustainability with their recycled materials line is inspiring. Just bought their Earth Day collection!",
      author: "Maya Patel",
      authorImage: "https://i.pravatar.cc/150?u=maya",
      brand: "Nike",
      brandDomain: "nike.com",
      likes: 345,
      timestamp: "2024-03-03T14:50:00Z"
    }
  ],
  starbucks: [
    {
      id: "starbucks-1",
      type: "image",
      content: "Starting my day with the new Starbucks spring collection. The lavender oat latte is everything! 💜",
      author: "Rachel Kim",
      authorImage: "https://i.pravatar.cc/150?u=rachel",
      brand: "Starbucks",
      brandDomain: "starbucks.com",
      likes: 567,
      image: "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=800&h=600",
      timestamp: "2024-03-02T08:15:00Z",
      socialLinks: {
        instagram: "racheldrinks"
      }
    },
    {
      id: "starbucks-2",
      type: "text",
      content: "Love how Starbucks is expanding their plant-based menu. The impossible breakfast sandwich is a hit!",
      author: "David Lee",
      authorImage: "https://i.pravatar.cc/150?u=david",
      brand: "Starbucks",
      brandDomain: "starbucks.com",
      likes: 234,
      timestamp: "2024-03-01T10:40:00Z"
    }
  ],
  airbnb: [
    {
      id: "airbnb-1",
      type: "image",
      content: "Found this hidden gem through Airbnb in Tuscany. The view is even better than the pictures!",
      author: "Sophie Anderson",
      authorImage: "https://i.pravatar.cc/150?u=sophie",
      brand: "Airbnb",
      brandDomain: "airbnb.com",
      likes: 890,
      image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600",
      timestamp: "2024-02-29T16:20:00Z",
      socialLinks: {
        instagram: "sophietravels"
      }
    },
    {
      id: "airbnb-2",
      type: "text",
      content: "Airbnb's new split stay feature made planning my Europe trip so much easier!",
      author: "Tom Brown",
      authorImage: "https://i.pravatar.cc/150?u=tom",
      brand: "Airbnb",
      brandDomain: "airbnb.com",
      likes: 456,
      timestamp: "2024-02-28T13:35:00Z"
    }
  ]
}

export const getFlawasByBrand = (brandId: string): Flawa[] => {
  return flawas[brandId] || []
}

export const getAllFlawas = (): Flawa[] => {
  return Object.values(flawas).flat()
}

export const getFeaturedFlawas = (brandId: string): Flawa[] => {
  const brandFlawas = flawas[brandId] || []
  return brandFlawas
    .sort((a, b) => b.likes - a.likes)
    .slice(0, 3)
}

export const getRecentFlawas = (brandId: string): Flawa[] => {
  const brandFlawas = flawas[brandId] || []
  return brandFlawas
    .sort((a, b) => new Date(b.timestamp || '').getTime() - new Date(a.timestamp || '').getTime())
    .slice(0, 3)
} 